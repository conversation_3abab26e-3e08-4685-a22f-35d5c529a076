targetScope = 'tenant'

param tenantId string = tenant().tenantId
param rootManagementGroupName string
param rootLandingZone array
param midManagementGroups array
param platformLandingZones array
param WorkloadsLandingZones array

@description('Klant code [Customer Code] onder de Root management group')
resource Tier1ManagementGroups 'Microsoft.Management/managementGroups@2023-04-01' = [for managementGroups in rootLandingZone: {
  name: managementGroups.id
  properties: {
    details: {
      parent: {
        id: tenantResourceId('Microsoft.Management/managementGroups', tenantId)
      }
    }
  }
}]

@description('Deze managementgroepen vallen onder de Klantcode')
resource Tier2ManagementGroups  'Microsoft.Management/managementGroups@2023-04-01' = [for managementGroups in midManagementGroups:  {
  name: managementGroups.id
  properties: {
    details: {
      parent: {
        id: tenantResourceId('Microsoft.Management/managementGroups',managementGroups.parentId)
      }
    }
  }
  dependsOn: [
    Tier1ManagementGroups
  ]
}]


@description('Managementgroepen onder Platform Management Group')
resource platformLandingZoneManagementGroups 'Microsoft.Management/managementGroups@2023-04-01' = [for managementGroups in platformLandingZones:  {
  name: managementGroups.id
  properties: {
    details: {
      parent: {
        id: tenantResourceId('Microsoft.Management/managementGroups',managementGroups.parentId)
      }
    }
  }
  dependsOn: [
    Tier2ManagementGroups
  ]
}]

@description('Managementgroepen onder Workloads Management Group')
resource LandingZoneManagementGroups 'Microsoft.Management/managementGroups@2023-04-01' = [for managementGroups in WorkloadsLandingZones:  {
  name: managementGroups.id
  properties: {
    details: {
      parent: {
        id: tenantResourceId('Microsoft.Management/managementGroups',managementGroups.parentId)
      }
    }
  }
  dependsOn: [
    platformLandingZoneManagementGroups
  ]
}]



















